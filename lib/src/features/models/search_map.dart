import 'dart:developer';

import 'package:page/src/core/extensions/extensions.dart';

class SearchMapModel {
  int? id;
  // double distance;
  double? lat;
  double? lng;
  // String? type;
  String? name;
  String? nameAr;
  int? maincategorytype;
  num? price;
  int? typeId;
  SearchMapModel({
    this.id,
  });
  SearchMapModel.fromJson(Map<String?, dynamic> json) {
    log('adfasdasfasfasfasfsf ${json}');
    id = json['id'];
    name = json['name'];
    nameAr = json['name_ar'];
    price = json['start_price'] ?? json['price'] ?? 0;
    lat = json['latitude'].toString().toDouble();
    lng = json['longitude'].toString().toDouble();
    typeId = json['type'] != null ? json['type']['id'] : '';
    // type = json['type'];
    maincategorytype = json['category'] != null ? json['category']['id'] : '';
  }
}
