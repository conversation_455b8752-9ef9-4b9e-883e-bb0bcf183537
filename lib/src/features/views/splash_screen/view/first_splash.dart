import 'dart:async';
import 'dart:developer';

import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:page/src/core/services/api.dart';
import 'package:page/src/features/repository/notification_controller.dart';
import 'package:page/src/features/views/splash_screen/services/splash_services.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:universal_platform/universal_platform.dart';

import '../../../../core/helper_methods/helper_methods.dart';
import '../../../../core/localization/app_language.dart';
import '../../home/<USER>';

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  print("Handling a background message: ${message.messageId}");
}

class FirstSplash extends StatefulWidget {
  const FirstSplash({super.key});

  @override
  _FirstSplash createState() => _FirstSplash();
}

class _FirstSplash extends State<FirstSplash>
    with SingleTickerProviderStateMixin {
  bool isLogin = false;
  final Duration _animationDuration =
      const Duration(seconds: 1, milliseconds: 1);
  late Animation<double> _animation;
  late AnimationController _animationController;

  Future<void> initPlugin() async {
    await NotificationController.handleNotifications();

    await Future.delayed(const Duration(seconds: 1));
    try {
      final TrackingStatus status =
          await AppTrackingTransparency.trackingAuthorizationStatus;
      if (status == TrackingStatus.notDetermined) {
        await AppTrackingTransparency.requestTrackingAuthorization();
      }
    } on PlatformException {}
  }

  @override
  void initState() {
    super.initState();

    if (UniversalPlatform.isIOS) {
      WidgetsBinding.instance.addPostFrameCallback((_) => initPlugin());
    }

    _animationController = AnimationController(
      vsync: this,
      duration: _animationDuration,
      lowerBound: 0.0,
      upperBound: 1.0,
    );

    _animation = CurvedAnimation(
        parent: _animationController, curve: Curves.fastOutSlowIn);

    _animation.addStatusListener((AnimationStatus state) {
      if (state == AnimationStatus.completed) {
        setState(() {});
      }
    });

    _animation.addListener(() {
      setState(() {});
    });

    Future.delayed(
        const Duration(
          milliseconds: 500,
        ), () {
      _animationController.forward();

      isLoggedIn();

      AppLanguage.getDefaultLanguage();

      HelperMethods.getDefaultCurrency()
          .then((value) => checkInternetAndNavigate());

      Api.getconfiguration();
    });
  }

  checkInternetAndNavigate() async {
    // final connectivityResult = await (Connectivity().checkConnectivity());
    // log('safasfsaf ${connectivityResult}');

    // if (connectivityResult.contains(ConnectivityResult.none)) {
    //   Navigator.of(context).pushReplacement(
    //       MaterialPageRoute(builder: (BuildContext context) => const Home()));
    // } else {
    getHomeData().then((value) async {
      Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (BuildContext context) => const Home()));
      // isLogin
      //     ?

      // : Navigator.of(context).pushReplacement(MaterialPageRoute(
      //     builder: (BuildContext context) => const SecondSplash()));
    });
    // }
  }

  final _splashServices = SplashServices();

  @override
  void dispose() {
    super.dispose();
    _animationController.dispose();
  }

  void isLoggedIn() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();

    var islogged = _prefs.getBool('is_logged');

    if (islogged == true) {
      setState(() {
        isLogin = true;
      });
    } else {
      setState(() {
        isLogin = false;
      });
    }
    print(isLogin);
  }

  Future<void> getHomeData() async {
    final value = await Api.gethome();
    setState(() {
      // featuredVideos = value.featuredvideo;

      newProjects = value.newProjects;
      categoryList = value.category.length > 10
          ? value.category.sublist(0, 9)
          : value.category;
      properties = value.luxury;
      holidayHomes = value.holidayHomes;
      allTypes = value.types;
      apartmentProjects = value.type1;
      villaProjects = value.type2;
      luxuryProjects = value.type3;

      log('SFAFASFSAF ${apartmentProjects.length} ffsfsf ${value.type1}');
    });

    _splashServices.initControllersForHomeData();

    await Future.delayed(const Duration(seconds: 1));
  }

  // void getReels(int size) async {
  //   try {
  //     final value = await Api.getreels(size);
  //
  //     if (value != null) {
  //       stories.clear();
  //
  //       stories.addAll(value.category);
  //     }
  //   } catch (e) {
  //     log('Error: $e');
  //   }
  // }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    ScreenUtil.init(context);
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.sizeOf(context).width;

    final isIpad = width > 600;

    final imageSize = isIpad ? 450 : 300;

    final imageAnimatedSize = isIpad ? 450 : 300;

    final Widget svg2 = Align(
      alignment: Alignment.center,
      child: SizedBox(
        width: imageSize * _animation.value,
        height: imageSize * _animation.value,
        child: Image.asset(
          'assets/images/splash.png',
          fit: BoxFit.contain,
        ),
      ),
    );

    return SafeArea(
        top: false,
        child: Scaffold(
            body: Stack(
          children: [
            Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                // gradient: LinearGradient(
                //   begin: const FractionalOffset(0, 0),
                //   end: const FractionalOffset(0, 1),
                //   colors: <Color>[
                //     const Color(0xFF27b4a8),
                //     const Color(0xFF27b4a8).withOpacity(0.5)
                //   ],
                // ),
              ),
              child: Stack(
                children: <Widget>[
                  Positioned.fill(
                    child: Align(
                      alignment: Alignment.center,
                      child: SizedBox(
                        width: imageAnimatedSize * _animation.value,
                        height: imageAnimatedSize * _animation.value,
                        child: svg2,
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        )));
  }

  void initNotifications() {
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print(
          'Message title: ${message.notification?.title}, body: ${message.notification?.body}, data: ${message.data}');
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print('A new onMessageOpenedApp event was published!');
    });

    FirebaseMessaging.instance
        .getInitialMessage()
        .then((RemoteMessage? message) {
      if (message != null) {
        print('Message data: ${message.data}');
        print('Message data: ${message.notification}');
      }
    });
  }
}
